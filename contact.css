/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Playfair Display", serif;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

/* Navigation Styles (matching project) */
.navbar {
  background: rgba(106, 174, 216, 0.5);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo span {
  color: var(--black, #090a04);
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-size: 1.2rem;
}

.nav {
  display: flex;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 1.2rem;
  align-items: center;
}

.nav-links a {
  color: rgba(9, 10, 4, 0.6);
  font-family: "Playfair Display", serif;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 10px;
}

.nav-links .btn {
  background-color: transparent;
  border: 1px solid rgba(9, 10, 4, 0.6);
  border-radius: 5px;
  padding: 8px 16px;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: rgba(106, 174, 216, 0.5);
  margin-top: 10px;
  padding: 10px 0;
  border-radius: 5px;
  list-style: none;
  z-index: 999;
}

.dropdown-content li a {
  display: block;
  padding: 5px 15px;
  white-space: nowrap;
  color: rgba(9, 10, 4, 0.6);
}

.dropdown.show .dropdown-content {
  display: block;
}

/* Mobile menu toggle */
.menu-toggle {
  display: none;
  font-size: 24px;
  background: transparent;
  color: var(--black, #090a04);
  border: none;
  cursor: pointer;
}

/* Page Header */
.page-header {
  padding: 3rem 5%;
  background: linear-gradient(to bottom, #6aaed880 0%, #f8f8f8 17.5%);
  color: #333;
}

.header-content {
  text-align: center;
  margin-bottom: 2.5rem;
}

.header-content .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.page-header h1 {
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.page-header h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.page-header h1::after {
  content: "📞";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  color: #c19f32;
}

.page-intro {
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact Section */
.contact-section {
  padding: 4rem 5%;
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  color: #090a04;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 500px;
  margin: 0 auto;
}

/* Contacts Grid */
.contacts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

/* Contact Cards */
.contact-card {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-top: 4px solid #c19f32;
  position: relative;
  overflow: hidden;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.contact-card.royal {
  border-top-color: #c19f32;
  background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.contact-card.administrative {
  border-top-color: #6aaed8;
}

.contact-card.community {
  border-top-color: #28a745;
}

.contact-card.security {
  border-top-color: #dc3545;
}

/* Contact Header */
.contact-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.contact-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(193, 159, 50, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.contact-card.royal .contact-icon {
  background: rgba(193, 159, 50, 0.2);
}

.contact-card.administrative .contact-icon {
  background: rgba(106, 174, 216, 0.1);
}

.contact-card.community .contact-icon {
  background: rgba(40, 167, 69, 0.1);
}

.contact-card.security .contact-icon {
  background: rgba(220, 53, 69, 0.1);
}

.contact-title h3 {
  font-size: 1.3rem;
  color: #090a04;
  margin-bottom: 0.2rem;
  font-weight: 700;
}

.contact-title h4 {
  font-size: 1.1rem;
  color: #c19f32;
  margin-bottom: 0.3rem;
  font-weight: 600;
}

.contact-role {
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
}

/* Contact Info */
.contact-info {
  margin-bottom: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.contact-item:hover {
  background: rgba(193, 159, 50, 0.05);
}

.info-icon {
  font-size: 1.1rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.info-text {
  font-size: 0.95rem;
  color: #333;
  font-weight: 500;
}

/* Contact Actions */
.contact-actions {
  display: flex;
  gap: 0.8rem;
  flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
  padding: 0.7rem 1.3rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  flex: 1;
  min-width: 120px;
}

.btn-primary {
  background: #6aaed8;
  color: white;
}

.btn-primary:hover {
  background: #5a9bc8;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(106, 174, 216, 0.3);
}

.btn-primary.emergency {
  background: #dc3545;
}

.btn-primary.emergency:hover {
  background: #c82333;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #6aaed8;
  border: 2px solid #6aaed8;
}

.btn-secondary:hover {
  background: #6aaed8;
  color: white;
  transform: translateY(-2px);
}

/* Emergency Section */
.emergency-section {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 3rem 5%;
  text-align: center;
}

.emergency-container h2 {
  font-size: 2.2rem;
  margin-bottom: 2rem;
  font-weight: 700;
}

.emergency-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.emergency-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.emergency-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.emergency-icon {
  font-size: 2.5rem;
  display: block;
  margin-bottom: 0.8rem;
}

.emergency-card h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.emergency-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #ffd700;
  margin: 0;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
    flex-wrap: wrap;
  }

  .logo span {
    font-size: 1.1rem;
  }

  .menu-toggle {
    display: block;
    order: 3;
  }

  .nav {
    display: none;
    width: 100%;
    order: 4;
    margin-top: 1rem;
  }

  .nav.active {
    display: block;
  }

  .nav-links {
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(106, 174, 216, 0.8);
    padding: 1rem;
    border-radius: 5px;
  }

  .nav-links a {
    padding: 10px;
    border-bottom: 1px solid rgba(9, 10, 4, 0.1);
  }

  .dropdown-content {
    position: static;
    display: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.1);
    margin-top: 5px;
  }

  .dropdown.show .dropdown-content {
    display: block;
  }

  .page-header {
    padding: 2rem 3%;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .contact-section {
    padding: 2rem 3%;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .contacts-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-card {
    padding: 1.5rem;
  }

  .contact-header {
    flex-direction: column;
    text-align: center;
    gap: 0.8rem;
  }

  .contact-actions {
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    min-width: auto;
  }

  .emergency-section {
    padding: 2rem 3%;
  }

  .emergency-container h2 {
    font-size: 1.8rem;
  }

  .emergency-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .emergency-card {
    padding: 1rem;
  }

  .emergency-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.8rem;
  }

  .section-header h2 {
    font-size: 1.6rem;
  }

  .contacts-grid {
    gap: 1rem;
  }

  .contact-card {
    padding: 1.2rem;
  }

  .contact-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
  }

  .contact-title h3 {
    font-size: 1.1rem;
  }

  .emergency-grid {
    grid-template-columns: 1fr;
  }

  .emergency-container h2 {
    font-size: 1.5rem;
  }
}
