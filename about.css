* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: "Lato", sans-serif;
  background-color: #f8f8f8;
}

/* Navbar */
.navbar {
  background: rgba(106, 174, 216, 0.5);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo span {
  color: var(--black, #090a04);
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-size: 1.2rem;
}

.nav {
  display: flex;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 1.2rem;
  align-items: center;
}

.nav-links a {
  color: rgba(9, 10, 4, 0.6);
  font-family: "Playfair Display", serif;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 10px;
}

.nav-links .btn {
  background-color: transparent;
  border: 1px solid rgba(9, 10, 4, 0.6);
  border-radius: 5px;
  padding: 8px 16px;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: rgba(106, 174, 216, 0.5);
  margin-top: 10px;
  padding: 10px 0;
  border-radius: 5px;
  list-style: none;
  z-index: 999;
}

.dropdown-content li a {
  display: block;
  padding: 5px 15px;
  white-space: nowrap;
  color: rgba(9, 10, 4, 0.6);
}

.dropdown.show .dropdown-content {
  display: block;
}

/* Mobile menu toggle */
.menu-toggle {
  display: none;
  font-size: 24px;
  background: transparent;
  color: var(--black, #090a04);
  border: none;
  cursor: pointer;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
    flex-wrap: wrap;
  }

  .menu-toggle {
    display: block;
    order: 3;
  }

  .nav {
    display: none;
    width: 100%;
    order: 4;
    margin-top: 1rem;
  }

  .nav.active {
    display: block;
  }

  .nav-links {
    flex-direction: column;
    gap: 0.5rem;
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .nav-links a {
    padding: 0.8rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
  }

  .nav-links a:hover {
    background: rgba(106, 174, 216, 0.1);
  }

  .dropdown-content {
    position: static;
    display: block;
    background: #f8f9fa;
    margin: 0.5rem 0 0 1rem;
    padding: 0.5rem 0;
    border-radius: 4px;
    box-shadow: none;
  }

  .dropdown-content li a {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
    flex-wrap: wrap;
  }

  .logo span {
    font-size: 1.1rem;
  }

  .menu-toggle {
    display: block;
    order: 2;
  }

  .nav {
    display: none;
    width: 100%;
    order: 3;
    margin-top: 1rem;
  }

  .nav.active {
    display: block;
  }

  .nav-links {
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .nav-links a {
    padding: 0.8rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }

  .nav-links a:hover {
    background: rgba(106, 174, 216, 0.1);
  }

  .dropdown-content {
    position: static;
    display: none;
    background: rgba(106, 174, 216, 0.1);
    margin: 0.5rem 0 0 1rem;
    padding: 0.5rem 0;
    border-radius: 5px;
    box-shadow: none;
  }

  .dropdown.show .dropdown-content {
    display: block;
  }

  .dropdown-content li a {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Our History Section */
#our-history {
  padding: 4rem 5%;
  background: linear-gradient(to bottom, #6aaed880 0%, #f8f8f8 17.5%);
  color: #333;
}

.history-container {
  max-width: 1200px;
  margin: 0 auto;
}

.history-header {
  text-align: center;
  margin-bottom: 3rem;
}

.history-header .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.history-header h1 {
  color: var(--black, #090a04);
  font-size: 3rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
}

.history-header p {
  font-size: 1.1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.history-content {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  align-items: center;
  margin-bottom: 3rem;
}

.history-text {
  flex: 1 1 50%;
  min-width: 300px;
}

.history-text h3 {
  font-size: 1.5rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.history-text .icon {
  font-size: 1.8rem;
  color: #c19f32;
}

.history-text p {
  font-size: 1rem;
  line-height: 1.7;
  color: #444;
  margin-bottom: 1rem;
}

.history-image {
  flex: 1 1 40%;
  min-width: 300px;
  text-align: center;
}

.history-image img {
  width: 100%;
  max-width: 400px;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.timeline-section {
  background-color: #fff;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  margin-top: 2rem;
}

.timeline-header {
  text-align: center;
  margin-bottom: 2rem;
}

.timeline-header h3 {
  font-size: 2rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  margin-bottom: 0.5rem;
}

.timeline-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.timeline-item {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #c19f32;
  transition: transform 0.3s ease;
}

.timeline-item:hover {
  transform: translateY(-5px);
}

.timeline-item h4 {
  font-size: 1.2rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.timeline-item .year {
  background: #c19f32;
  color: #fff;
  padding: 0.2rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: bold;
}

.timeline-item p {
  color: #555;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  #our-history {
    padding: 2rem 3%;
  }

  .history-header h1 {
    font-size: 2.2rem;
  }

  .history-content {
    flex-direction: column;
    gap: 2rem;
  }

  .timeline-items {
    grid-template-columns: 1fr;
  }

  .timeline-section {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .history-header h1 {
    font-size: 1.8rem;
  }

  .history-text h3 {
    font-size: 1.3rem;
  }

  .timeline-header h3 {
    font-size: 1.6rem;
  }
}

/* Ruling Houses Section */
#rulling-house {
  padding: 3rem 5%;
  background: #ffffff;
  color: #333;
}

.rulling-house-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.rulling-house-header .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.rulling-house h1 {
  text-align: center;
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.rulling-house h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.rulling-house h1::after {
  content: "👑";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  color: #c19f32;
}

.rulling-house-intro {
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto 2rem auto;
}

.houses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.rulling-house-family {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
  border-top: 3px solid #c19f32;
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.rulling-house-family:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-top-color: #6aaed8;
}

.rulling-house-family::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #c19f32 50%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rulling-house-family:hover::before {
  opacity: 1;
}

.rulling-house-family img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #c19f32;
  box-shadow: 0 3px 10px rgba(193, 159, 50, 0.2);
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.rulling-house-family:hover img {
  border-color: #6aaed8;
  box-shadow: 0 4px 15px rgba(106, 174, 216, 0.3);
  transform: scale(1.05);
}

.family-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0 0.5rem;
}

.family-info div {
  text-align: center;
  flex: 1;
}

.family-info h3 {
  font-size: 0.8rem;
  color: #c19f32;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  margin-bottom: 0.3rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.family-info p {
  font-size: 1rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  margin: 0;
  text-transform: capitalize;
}

.house-motto {
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 2px solid #c19f32;
}

/* Responsive Design for Ruling Houses */
@media (max-width: 768px) {
  #rulling-house {
    padding: 2rem 3%;
  }

  .rulling-house h1 {
    font-size: 2rem;
  }

  .houses-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.2rem;
  }

  .rulling-house-family {
    padding: 1.2rem;
  }

  .rulling-house-family img {
    width: 70px;
    height: 70px;
  }

  .family-info h3 {
    font-size: 0.75rem;
  }

  .family-info p {
    font-size: 0.9rem;
  }

  .house-motto {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .rulling-house h1 {
    font-size: 1.8rem;
  }

  .houses-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .rulling-house-family {
    padding: 1rem;
  }

  .rulling-house-family img {
    width: 60px;
    height: 60px;
  }

  .family-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .family-info div {
    margin-bottom: 0.3rem;
  }
}

/* Traditional Age Groups Section */
.Traditional-Age-Group {
  padding: 4rem 5%;
  background: #ffffff;
  color: #333;
}

.age-group-header {
  text-align: center;
  margin-bottom: 3rem;
}

.age-group-header .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.Traditional-Age-Group h1 {
  text-align: center;
  color: var(--black, #090a04);
  font-size: 2.8rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.Traditional-Age-Group h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.Traditional-Age-Group h1::after {
  content: "👥";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  color: #c19f32;
}

.age-group-intro {
  font-size: 1.1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 3rem auto;
}

.age-groups-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.groups-section-okunrin,
.groups-section-obirin {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.groups-section-okunrin:hover,
.groups-section-obirin:hover {
  transform: translateY(-5px);
}

.groups-section-okunrin {
  border-top: 5px solid #4a90e2;
}

.groups-section-obirin {
  border-top: 5px solid #e24a90;
}

.groups-section-okunrin h2,
.groups-section-obirin h2 {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  margin: 0;
  font-size: 1.8rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  text-align: center;
  position: relative;
}

.groups-section-okunrin h2::before {
  content: "♂";
  color: #4a90e2;
  margin-right: 0.5rem;
  font-size: 1.5rem;
}

.groups-section-obirin h2::before {
  content: "♀";
  color: #e24a90;
  margin-right: 0.5rem;
  font-size: 1.5rem;
}

.groups-table-head {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 2rem;
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
}

.groups-table-head h3 {
  font-size: 0.9rem;
  color: #c19f32;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
  text-align: center;
}

.group-contents {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 2rem;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.group-contents:hover {
  background: #f8f9fa;
}

.group-contents:last-child {
  border-bottom: none;
}

.group-contents p {
  margin: 0;
  font-size: 0.9rem;
  color: #333;
  text-align: center;
  padding: 0.5rem;
  font-weight: 500;
}

.group-contents p:first-child {
  font-weight: 700;
  color: #090a04;
  text-align: left;
  font-family: "Playfair Display", serif;
}

/* Age range indicators */
.group-contents:nth-child(2) p:first-child::after,
.group-contents:nth-child(3) p:first-child::after,
.group-contents:nth-child(4) p:first-child::after {
  content: " 🏆";
  color: #c19f32;
  font-size: 0.8rem;
}

.group-contents:nth-last-child(-n + 3) p:first-child::after {
  content: " 🌟";
  color: #4a90e2;
  font-size: 0.8rem;
}

/* Responsive Design for Traditional Age Groups */
@media (max-width: 1024px) {
  .Traditional-Age-Group {
    padding: 3rem 4%;
  }

  .age-groups-container {
    gap: 2rem;
  }

  .groups-table-head,
  .group-contents {
    grid-template-columns: 2fr 0.8fr 0.8fr 0.8fr 0.8fr;
    padding: 0.8rem 1.5rem;
  }

  .groups-table-head h3 {
    font-size: 0.8rem;
  }

  .group-contents p {
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .Traditional-Age-Group {
    padding: 2rem 3%;
  }

  .Traditional-Age-Group h1 {
    font-size: 2.2rem;
  }

  .age-groups-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .groups-section-okunrin h2,
  .groups-section-obirin h2 {
    font-size: 1.5rem;
    padding: 1.2rem 1.5rem;
  }

  .groups-table-head,
  .group-contents {
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    padding: 0.8rem 1rem;
    gap: 0.5rem;
  }

  .groups-table-head h3 {
    font-size: 0.75rem;
  }

  .group-contents p {
    font-size: 0.8rem;
    padding: 0.3rem;
  }
}

@media (max-width: 480px) {
  .Traditional-Age-Group h1 {
    font-size: 1.8rem;
  }

  .age-group-intro {
    font-size: 1rem;
  }

  .groups-section-okunrin h2,
  .groups-section-obirin h2 {
    font-size: 1.3rem;
    padding: 1rem;
  }

  .groups-table-head,
  .group-contents {
    grid-template-columns: 1.5fr 0.8fr 0.8fr 0.8fr 0.8fr;
    padding: 0.6rem 0.8rem;
    gap: 0.3rem;
  }

  .groups-table-head h3 {
    font-size: 0.7rem;
  }

  .group-contents p {
    font-size: 0.75rem;
    padding: 0.2rem;
  }

  .group-contents p:first-child {
    font-size: 0.8rem;
  }
}

/* Special styling for mobile table view */
@media (max-width: 360px) {
  .groups-table-head,
  .group-contents {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .groups-table-head {
    display: none;
  }

  .group-contents {
    display: block;
    padding: 1rem;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 0.5rem;
  }

  .group-contents p {
    display: block;
    text-align: left;
    margin-bottom: 0.3rem;
    font-size: 0.85rem;
  }

  .group-contents p:first-child {
    font-size: 1.1rem;
    color: #c19f32;
    margin-bottom: 0.8rem;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.5rem;
  }

  .group-contents p:nth-child(2)::before {
    content: "Born: ";
    font-weight: 600;
    color: #666;
  }
  .group-contents p:nth-child(3)::before {
    content: "To: ";
    font-weight: 600;
    color: #666;
  }
  .group-contents p:nth-child(4)::before {
    content: "Age: ";
    font-weight: 600;
    color: #666;
  }
  .group-contents p:nth-child(5)::before {
    content: "Range: ";
    font-weight: 600;
    color: #666;
  }
}

/* Club and Societies Section */
.club-and-societies {
  padding: 3rem 5%;
  background: #ffffff;
  color: #333;
}

.club-and-societies h1 {
  text-align: center;
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.club-and-societies h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.club-and-societies h1::after {
  content: "🤝";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  color: #c19f32;
}

.clubs-intro {
  text-align: center;
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 2.5rem auto;
}

.clubs-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.clubs {
  background: white;
  padding: 1.2rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #c19f32;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.clubs:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border-left-color: #6aaed8;
}

.clubs::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #c19f32 50%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.clubs:hover::before {
  opacity: 1;
}

.club-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.club-name,
.club-president {
  flex: 1;
  text-align: center;
}

.club-name h3,
.club-president h3 {
  font-size: 0.75rem;
  color: #c19f32;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  margin-bottom: 0.4rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.club-name h3::before {
  content: "🏛️ ";
  font-size: 0.8rem;
}

.club-president h3::before {
  content: "👤 ";
  font-size: 0.8rem;
}

.club-name p,
.club-president p {
  font-size: 0.95rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  margin: 0;
  text-transform: capitalize;
  line-height: 1.3;
}

.club-president p {
  color: #666;
  font-style: italic;
  font-weight: 400;
}

/* Add category badges for different club types */
.clubs[data-category="social"]::after {
  content: "Social";
  position: absolute;
  top: 8px;
  right: 8px;
  background: #e3f2fd;
  color: #1976d2;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.clubs[data-category="diaspora"]::after {
  content: "Diaspora";
  position: absolute;
  top: 8px;
  right: 8px;
  background: #f3e5f5;
  color: #7b1fa2;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.clubs[data-category="women"]::after {
  content: "Women";
  position: absolute;
  top: 8px;
  right: 8px;
  background: #fce4ec;
  color: #c2185b;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.clubs[data-category="youth"]::after {
  content: "Youth";
  position: absolute;
  top: 8px;
  right: 8px;
  background: #e8f5e8;
  color: #388e3c;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* Responsive Design for Clubs & Societies */
@media (max-width: 768px) {
  .club-and-societies {
    padding: 2rem 3%;
  }

  .club-and-societies h1 {
    font-size: 2rem;
  }

  .clubs-container {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .clubs {
    padding: 1rem;
  }

  .club-info {
    flex-direction: column;
    gap: 0.8rem;
  }

  .club-name h3,
  .club-president h3 {
    font-size: 0.7rem;
  }

  .club-name p,
  .club-president p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .club-and-societies h1 {
    font-size: 1.8rem;
  }

  .clubs-container {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .clubs {
    padding: 0.8rem;
  }

  .club-name h3,
  .club-president h3 {
    font-size: 0.65rem;
  }

  .club-name p,
  .club-president p {
    font-size: 0.85rem;
  }

  .clubs[data-category]::after {
    font-size: 0.6rem;
    padding: 1px 4px;
  }
}

/* Places of Interest Section */
.place-of-intrest {
  padding: 3rem 5%;
  background: #ffffff;
  color: #333;
}

.places-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.places-header .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.places-header h1 {
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.places-header h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.places-header h1::after {
  content: "🏛️";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  color: #c19f32;
}

.places-intro {
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

.places-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.place-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border-top: 3px solid #c19f32;
  position: relative;
}

.place-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-top-color: #6aaed8;
}

.place-image {
  position: relative;
  overflow: hidden;
  height: 160px;
}

.place-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.place-card:hover .place-image img {
  transform: scale(1.05);
}

.place-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.place-card:hover .place-image::after {
  opacity: 1;
}

.place-info {
  padding: 1.2rem;
}

.place-info h3 {
  font-size: 1.1rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  margin-bottom: 0.6rem;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.place-info h3::before {
  content: "📍";
  font-size: 0.9rem;
  color: #c19f32;
}

.place-info p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

/* Add category indicators */
.place-card[data-type="religious"] .place-info h3::before {
  content: "🕌";
}

.place-card[data-type="cultural"] .place-info h3::before {
  content: "🏛️";
}

.place-card[data-type="natural"] .place-info h3::before {
  content: "🌿";
}

.place-card[data-type="civic"] .place-info h3::before {
  content: "🏢";
}

.place-card[data-type="commercial"] .place-info h3::before {
  content: "🏪";
}

/* Responsive Design for Places of Interest */
@media (max-width: 768px) {
  .place-of-intrest {
    padding: 2rem 3%;
  }

  .places-header h1 {
    font-size: 2rem;
  }

  .places-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.2rem;
  }

  .place-image {
    height: 140px;
  }

  .place-info {
    padding: 1rem;
  }

  .place-info h3 {
    font-size: 1rem;
  }

  .place-info p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .places-header h1 {
    font-size: 1.8rem;
  }

  .places-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .place-image {
    height: 120px;
  }

  .place-info {
    padding: 0.8rem;
  }

  .place-info h3 {
    font-size: 0.95rem;
  }

  .place-info p {
    font-size: 0.8rem;
  }
}

/* Quarters Section */
#quaters {
  padding: 3rem 5%;
  background: #ffffff;
  color: #333;
  position: relative;
}

#quaters::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="%23c19f32" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

.quaters > * {
  position: relative;
  z-index: 1;
}

.quaters h1 {
  text-align: center;
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 0.8rem;
  text-transform: capitalize;
  position: relative;
}

.quaters h1::before {
  content: "🏘️";
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  color: #c19f32;
}

.quaters h1::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #c19f32 50%,
    transparent 100%
  );
}

/* Add intro text */
.quarters-intro {
  text-align: center;
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.5;
  max-width: 500px;
  margin: 0 auto 2rem auto;
  font-style: italic;
}

/* Quarters Grid Layout */
.quarters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.quater-container {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  border: 1px solid transparent;
  border-image: linear-gradient(45deg, #c19f32, #6aaed8, #c19f32) 1;
  padding: 1rem;
  position: relative;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.quater-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(193, 159, 50, 0.05) 0%,
    rgba(106, 174, 216, 0.05) 100%
  );
  z-index: -1;
  transition: opacity 0.3s ease;
}

.quater-container:hover::before {
  opacity: 0.8;
}

.quater-container:hover {
  transform: translateY(-3px);
  border-image: linear-gradient(45deg, #6aaed8, #c19f32, #6aaed8) 1;
}

.quater-container h3 {
  font-size: 1.1rem;
  color: #c19f32;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  margin-bottom: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  position: relative;
  padding-left: 1.2rem;
  text-align: center;
}

.quater-container h3::before {
  content: "📍";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1rem;
}

.quater-leader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  padding: 0.6rem;
  background: rgba(193, 159, 50, 0.1);
  border-left: 2px solid #c19f32;
  border-radius: 0 4px 4px 0;
}

.quater-leader h4 {
  font-size: 0.75rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  min-width: 80px;
}

.quater-leader p {
  font-size: 0.95rem;
  color: #c19f32;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  margin: 0;
  text-transform: capitalize;
}

.quater-members {
  padding: 0.6rem;
  background: rgba(106, 174, 216, 0.1);
  border-left: 2px solid #6aaed8;
  border-radius: 0 4px 4px 0;
}

.quater-members h4 {
  font-size: 0.75rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  margin-bottom: 0.6rem;
  text-transform: uppercase;
  letter-spacing: 0.2px;
}

.quater-members p {
  display: inline-block;
  background: rgba(255, 255, 255, 0.8);
  color: #090a04;
  padding: 0.2rem 0.5rem;
  margin: 0.15rem 0.2rem 0.15rem 0;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: capitalize;
  border: 1px solid rgba(106, 174, 216, 0.3);
  transition: all 0.3s ease;
}

.quater-members p:hover {
  background: #6aaed8;
  color: white;
  transform: translateY(-1px);
}

/* Responsive Design for Quarters */
@media (max-width: 1024px) {
  .quarters-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  #quaters {
    padding: 2rem 3%;
  }

  .quaters h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .quaters h1::before {
    top: -35px;
    font-size: 1.5rem;
  }

  .quarters-intro {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .quarters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .quater-container {
    padding: 0.8rem;
  }

  .quater-container h3 {
    font-size: 1rem;
    padding-left: 1rem;
    margin-bottom: 0.6rem;
  }

  .quater-container h3::before {
    font-size: 0.9rem;
  }

  .quater-leader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
    padding: 0.5rem;
    margin-bottom: 0.6rem;
  }

  .quater-leader h4 {
    font-size: 0.7rem;
    min-width: auto;
  }

  .quater-leader p {
    font-size: 0.85rem;
  }

  .quater-members {
    padding: 0.5rem;
  }

  .quater-members h4 {
    font-size: 0.7rem;
    margin-bottom: 0.5rem;
  }

  .quater-members p {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
    margin: 0.1rem 0.15rem 0.1rem 0;
  }
}

@media (max-width: 480px) {
  .quaters h1 {
    font-size: 1.6rem;
  }

  .quaters h1::before {
    top: -30px;
    font-size: 1.2rem;
  }

  .quarters-intro {
    font-size: 0.85rem;
  }

  .quater-container {
    padding: 0.6rem;
  }

  .quater-container h3 {
    font-size: 0.9rem;
    padding-left: 0.8rem;
  }

  .quater-leader,
  .quater-members {
    padding: 0.4rem;
  }

  .quater-leader h4,
  .quater-members h4 {
    font-size: 0.65rem;
  }

  .quater-leader p {
    font-size: 0.8rem;
  }

  .quater-members p {
    font-size: 0.6rem;
    padding: 0.1rem 0.3rem;
  }
}

/* Council of Chiefs Section */
#Council-of-chiefs {
  padding: 4rem 5%;
  background: #ffffff;
  color: #333;
}

.Council-of-chiefs h1 {
  text-align: center;
  color: var(--black, #090a04);
  font-size: 2.8rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.Council-of-chiefs h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.Council-of-chiefs h1::after {
  content: "⚔️";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
}

.chiefs-intro {
  text-align: center;
  font-size: 1.1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 3rem auto;
}

.chiefs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.chiefs-container {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #c19f32;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chiefs-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-left-color: #6aaed8;
}

.chiefs-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #c19f32 50%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chiefs-container:hover::before {
  opacity: 1;
}

.chiefs-container img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #c19f32;
  box-shadow: 0 4px 15px rgba(193, 159, 50, 0.3);
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto 1.5rem auto;
}

.chiefs-container:hover img {
  border-color: #6aaed8;
  box-shadow: 0 6px 20px rgba(106, 174, 216, 0.4);
  transform: scale(1.05);
}

.chief-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.chief-title,
.chief-Name {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 0.8rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-bottom: 2px solid #c19f32;
}

.chief-title h3,
.chief-Name h3 {
  font-size: 0.9rem;
  color: #c19f32;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chief-title p,
.chief-Name p {
  font-size: 1.1rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  margin: 0;
  text-transform: capitalize;
}

.chief-designation {
  text-align: center;
  margin-top: 1rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, #c19f32, #d4af3a);
  color: white;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Council of Chiefs Responsive Design */
@media (max-width: 768px) {
  #Council-of-chiefs {
    padding: 2rem 3%;
  }

  .Council-of-chiefs h1 {
    font-size: 2.2rem;
  }

  .chiefs-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chiefs-container {
    padding: 1.5rem;
  }

  .chiefs-container img {
    width: 80px;
    height: 80px;
  }

  .chief-info-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .chief-title h3,
  .chief-Name h3 {
    font-size: 0.8rem;
  }

  .chief-title p,
  .chief-Name p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .Council-of-chiefs h1 {
    font-size: 1.8rem;
  }

  .chiefs-container {
    padding: 1rem;
  }

  .chiefs-container img {
    width: 70px;
    height: 70px;
  }

  .chief-title,
  .chief-Name {
    padding: 0.6rem;
  }
}

/* Functional Group & Authority Section */
#functional-group-authority {
  padding: 4rem 5%;
  background: #ffffff;
  color: #333;
}

.functional-group-header {
  text-align: center;
  margin-bottom: 3rem;
}

.functional-group-header .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.functional-group-authority h1 {
  text-align: center;
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.functional-group-authority h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.functional-group-authority h1::after {
  content: "⚖️";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
}

.functional-group-intro {
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 3rem auto;
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.groups-container {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border-left: 5px solid #c19f32;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.groups-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-left-color: #6aaed8;
}

.groups-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #c19f32 50%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.groups-container:hover::before {
  opacity: 1;
}

.groups-container h2 {
  font-size: 1.4rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  padding-bottom: 0.5rem;
}

.groups-container h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: #c19f32;
}

.leader-section {
  margin-bottom: 1.5rem;
}

.leader-label {
  font-size: 0.9rem;
  color: #c19f32;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.leader-label::before {
  content: "👑";
  font-size: 1rem;
}

.leader {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid #c19f32;
  text-align: center;
}

.leader h3 {
  font-size: 1.2rem;
  color: #090a04;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  margin: 0;
  text-transform: capitalize;
}

.members-section {
  margin-top: 1.5rem;
}

.members-label {
  font-size: 0.9rem;
  color: #6aaed8;
  font-family: "Playfair Display", serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.members-label::before {
  content: "👥";
  font-size: 1rem;
}

.members {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  justify-content: center;
}

.members p {
  background: linear-gradient(135deg, #6aaed8 0%, #c19f32 100%);
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
  text-transform: capitalize;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.members p:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  /* History Section Mobile */
  #our-history {
    padding: 2rem 3%;
  }

  .history-header h1 {
    font-size: 2.2rem;
  }

  .history-header p {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .history-content {
    flex-direction: column;
    gap: 2rem;
  }

  .history-text {
    min-width: auto;
  }

  .history-image {
    min-width: auto;
  }

  .history-image img {
    max-width: 300px;
  }

  .timeline-items {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .timeline-section {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }

  .timeline-item {
    padding: 1.2rem;
  }

  /* Functional Groups Mobile */
  #functional-group-authority {
    padding: 2rem 3%;
  }

  .functional-group-authority h1 {
    font-size: 2rem;
  }

  .groups-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .groups-container {
    padding: 1.5rem;
  }

  .groups-container h2 {
    font-size: 1.2rem;
  }

  .leader {
    padding: 0.8rem;
  }

  .leader h3 {
    font-size: 1.1rem;
  }

  .members p {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 480px) {
  /* Base adjustments */
  body {
    font-size: 14px;
  }

  /* History Section Mobile */
  #our-history {
    padding: 1.5rem 4%;
  }

  .history-header h1 {
    font-size: 1.8rem;
  }

  .history-header p {
    font-size: 0.9rem;
  }

  .history-text h3 {
    font-size: 1.3rem;
  }

  .history-text p {
    font-size: 0.9rem;
  }

  .history-image img {
    max-width: 250px;
  }

  .timeline-header h3 {
    font-size: 1.6rem;
  }

  .timeline-section {
    padding: 1rem;
  }

  .timeline-item {
    padding: 1rem;
  }

  .timeline-item h4 {
    font-size: 1.1rem;
  }

  .timeline-item p {
    font-size: 0.9rem;
  }

  /* Functional Groups Mobile */
  .functional-group-authority h1 {
    font-size: 1.8rem;
  }

  .groups-container {
    padding: 1rem;
  }

  .groups-container h2 {
    font-size: 1.1rem;
  }

  .leader-label,
  .members-label {
    font-size: 0.8rem;
  }

  .members {
    gap: 0.5rem;
  }

  .members p {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}
