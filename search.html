<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Search for Family & Friends - <PERSON><PERSON>-<PERSON><PERSON>ti</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="search.css" />
    <script src="javascript.js" defer></script>
  </head>
  <body>
    <header class="navbar">
      <div class="logo">👑 <span>OSI-EKITI</span></div>

      <!-- Mobile Menu Button -->
      <button class="menu-toggle" id="menuToggle">&#9776;</button>

      <nav class="nav" id="navMenu">
        <ul class="nav-links">
          <li><a href="index.html">Home</a></li>
          <li class="dropdown">
            <a href="#" class="dropdown-toggle">About Us ▾</a>
            <ul class="dropdown-content">
              <li><a href="about.html#our-history">Our History</a></li>
              <li>
                <a href="about.html#rulling-house">The Ruling Houses</a>
              </li>
              <li>
                <a href="about.html#functional-groups"
                  >The Functional Groups & Authority</a
                >
              </li>
              <li>
                <a href="about.html#council-of-chiefs">The Council Of Chiefs</a>
              </li>
              <li><a href="about.html#quarters">The Quarters</a></li>
              <li>
                <a href="about.html#traditional-age-groups"
                  >The Traditional Age Groups</a
                >
              </li>
              <li>
                <a href="about.html#club-and-societies">Club & Societies</a>
              </li>
              <li>
                <a href="about.html#place-of-intrest">Places of Interest</a>
              </li>
            </ul>
          </li>
          <li class="dropdown">
            <a href="#" class="dropdown-toggle">Community ▾</a>
            <ul class="dropdown-content">
              <li><a href="register.html">Register As Omo Osi</a></li>
              <li><a href="search.html">Search for Family & Friends</a></li>
            </ul>
          </li>
          <li class="dropdown">
            <a href="#" class="dropdown-toggle">Events ▾</a>
            <ul class="dropdown-content">
              <li><a href="events.html#current-events">Current Events</a></li>
              <li><a href="events.html#upcoming-events">Upcoming Events</a></li>
              <li><a href="events.html#monthly-events">Monthly Events</a></li>
              <li><a href="events.html#seasonal-events">Seasonal Events</a></li>
            </ul>
          </li>
          <li><a href="information-center.html">Information Center</a></li>
          <li><a href="projects.html">Projects</a></li>
          <li><a href="contact-us.html">Contact Us</a></li>
          <li><a href="donate.html" class="btn">Donate</a></li>
        </ul>
      </nav>
    </header>

    <!-- Hero Search Section -->
    <section class="hero-search">
      <div class="hero-container">
        <!-- Left Content -->
        <div class="hero-content">
          <h1 class="hero-title">Search for a Familiar Face</h1>
          <p class="hero-subtitle">
            Reconnect with people from home — near or far
          </p>

          <!-- Search Input -->
          <div class="search-container">
            <div class="search-input-wrapper">
              <svg
                class="search-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <input
                type="text"
                id="liveSearchInput"
                class="search-input"
                placeholder="Enter name, nickname, alias..."
                autocomplete="off"
              />
              <div
                id="searchLoader"
                class="search-loader"
                style="display: none"
              >
                <div class="loader-spinner"></div>
              </div>
            </div>
            <button type="button" class="search-btn" id="searchBtn">
              Find someone
            </button>
          </div>

          <!-- Live Search Results -->
          <div id="liveResults" class="live-results" style="display: none">
            <div id="resultsContainer" class="results-container">
              <!-- Results will be populated here -->
            </div>
          </div>
        </div>

        <!-- Right Illustration -->
        <div class="hero-illustration">
          <div class="illustration-container">
            <!-- Background Circles -->
            <div class="bg-circle circle-1"></div>
            <div class="bg-circle circle-2"></div>
            <div class="bg-circle circle-3"></div>
            <div class="bg-circle circle-4"></div>

            <!-- Main Illustration -->
            <div class="people-illustration">
              <img
                src="https://images.unsplash.com/photo-1529156069898-49953e39b3ac?w=400&h=300&fit=crop&crop=center"
                alt="Happy people celebrating"
                class="celebration-image"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <script>
      // Live Search Implementation
      document.addEventListener("DOMContentLoaded", function () {
        const searchInput = document.getElementById("liveSearchInput");
        const searchBtn = document.getElementById("searchBtn");
        const searchLoader = document.getElementById("searchLoader");
        const liveResults = document.getElementById("liveResults");
        const resultsContainer = document.getElementById("resultsContainer");

        let searchTimeout;
        let isSearching = false;

        // Live search as user types
        searchInput.addEventListener("input", function () {
          const query = this.value.trim();

          // Clear previous timeout
          clearTimeout(searchTimeout);

          if (query.length === 0) {
            hideLiveResults();
            return;
          }

          if (query.length < 2) {
            return; // Wait for at least 2 characters
          }

          // Show loader
          showLoader();

          // Debounce search - wait 300ms after user stops typing
          searchTimeout = setTimeout(() => {
            performLiveSearch(query);
          }, 300);
        });

        // Search button click
        searchBtn.addEventListener("click", function () {
          const query = searchInput.value.trim();
          if (query.length > 0) {
            performLiveSearch(query);
          }
        });

        // Enter key search
        searchInput.addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            e.preventDefault();
            const query = this.value.trim();
            if (query.length > 0) {
              performLiveSearch(query);
            }
          }
        });

        function showLoader() {
          searchLoader.style.display = "block";
        }

        function hideLoader() {
          searchLoader.style.display = "none";
        }

        function showLiveResults() {
          liveResults.style.display = "block";
        }

        function hideLiveResults() {
          liveResults.style.display = "none";
          hideLoader();
        }

        async function performLiveSearch(query) {
          if (isSearching) return;

          isSearching = true;
          showLoader();

          try {
            console.log("Searching for:", query);

            const response = await fetch(
              `/osi_ekiti_node/api/search?q=${encodeURIComponent(query)}`,
              {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                },
              }
            );

            const result = await response.json();
            console.log("Search response:", result);

            if (response.ok && result.success) {
              displayLiveResults(result.data, query);
            } else {
              displayError(result.error || "Search failed");
            }
          } catch (error) {
            console.error("Search error:", error);
            displayError("Unable to connect to search service");
          } finally {
            hideLoader();
            isSearching = false;
          }
        }

        function displayLiveResults(results, query) {
          showLiveResults();

          if (!results || results.length === 0) {
            resultsContainer.innerHTML = `
              <div class="no-results">
                <div class="no-results-icon">🔍</div>
                <h3>No results found</h3>
                <p>No members found matching "${query}"</p>
                <p class="suggestion">Try searching with a different name or family name</p>
              </div>
            `;
            return;
          }

          const resultsHtml = results
            .map(
              (member) => `
            <div class="result-card">
              <div class="result-avatar">
                <div class="avatar-circle">
                  ${member.fullname.charAt(0).toUpperCase()}
                </div>
              </div>
              <div class="result-info">
                <h3 class="result-name">${highlightMatch(
                  member.fullname,
                  query
                )}</h3>
                <p class="result-family">Family: ${highlightMatch(
                  member.family_name,
                  query
                )}</p>
                <div class="result-details">
                  <span class="result-quarters">📍 ${member.quarters}</span>
                  <span class="result-location">🏠 ${
                    member.current_address
                  }</span>
                </div>
                <div class="result-contact">
                  <div class="contact-item">
                    <span class="result-phone">📞 ${member.phone_no}</span>
                    <button class="copy-btn" onclick="copyToClipboard('${
                      member.phone_no
                    }', 'phone')" title="Copy phone number">
                      📋
                    </button>
                  </div>
                  <div class="contact-item">
                    <span class="result-email">✉️ ${member.email}</span>
                    <button class="copy-btn" onclick="copyToClipboard('${
                      member.email
                    }', 'email')" title="Copy email">
                      📋
                    </button>
                  </div>
                </div>
              </div>
            </div>
          `
            )
            .join("");

          resultsContainer.innerHTML = `
            <div class="results-header">
              <h3>Found ${results.length} member${
            results.length !== 1 ? "s" : ""
          }</h3>
              <p>Search results for "${query}"</p>
            </div>
            <div class="results-list">
              ${resultsHtml}
            </div>
          `;
        }

        function highlightMatch(text, query) {
          if (!query) return text;
          const regex = new RegExp(`(${query})`, "gi");
          return text.replace(regex, "<mark>$1</mark>");
        }

        function displayError(message) {
          showLiveResults();
          resultsContainer.innerHTML = `
            <div class="error-message">
              <div class="error-icon">⚠️</div>
              <h3>Search Error</h3>
              <p>${message}</p>
              <button onclick="location.reload()" class="retry-btn">Try Again</button>
            </div>
          `;
        }

        // Global function for copy to clipboard
        window.copyToClipboard = async function (text, type) {
          try {
            await navigator.clipboard.writeText(text);

            // Show success feedback
            const button = event.target;
            const originalText = button.textContent;

            button.textContent = "✅";
            button.style.backgroundColor = "#28a745";

            setTimeout(() => {
              button.textContent = originalText;
              button.style.backgroundColor = "";
            }, 2000);

            // Optional: Show toast notification
            showToast(
              `${
                type === "phone" ? "Phone number" : "Email"
              } copied to clipboard!`
            );
          } catch (err) {
            console.error("Failed to copy: ", err);

            // Fallback for older browsers
            const textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);

            showToast(
              `${
                type === "phone" ? "Phone number" : "Email"
              } copied to clipboard!`
            );
          }
        };

        // Toast notification function
        function showToast(message) {
          // Remove existing toast if any
          const existingToast = document.querySelector(".toast-notification");
          if (existingToast) {
            existingToast.remove();
          }

          // Create toast element
          const toast = document.createElement("div");
          toast.className = "toast-notification";
          toast.textContent = message;

          // Add to page
          document.body.appendChild(toast);

          // Show toast
          setTimeout(() => {
            toast.classList.add("show");
          }, 100);

          // Hide and remove toast
          setTimeout(() => {
            toast.classList.remove("show");
            setTimeout(() => {
              if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
              }
            }, 300);
          }, 3000);
        }
      });
    </script>
  </body>
</html>
