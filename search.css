/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Playfair Display", serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

/* Header Styles - White Background */
.navbar {
  background-color: #ffffff;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar .logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #8b4513;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar .logo span {
  color: #333;
}

/* Navigation Styles */
.nav {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-links a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-links a:hover {
  color: #8b4513;
}

.nav-links .btn {
  background-color: #8b4513;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.nav-links .btn:hover {
  background-color: #6d3410;
  color: white;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  min-width: 200px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  z-index: 1000;
  list-style: none;
  padding: 0.5rem 0;
}

.dropdown:hover .dropdown-content,
.dropdown.show .dropdown-content {
  display: block;
}

.dropdown-content li {
  margin: 0;
}

.dropdown-content a {
  display: block;
  padding: 0.5rem 1rem;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown-content a:hover {
  background-color: #f8f9fa;
  color: #8b4513;
}

/* Mobile Menu */
.menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #333;
}

/* Hero Search Section */
.hero-search {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.hero-container {
  max-width: 1200px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

/* Left Content */
.hero-content {
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* Search Container */
.search-container {
  margin-bottom: 2rem;
}

.search-input-wrapper {
  position: relative;
  margin-bottom: 1rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #666;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1.1rem;
  background-color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
  outline: none;
  border-color: #8b4513;
  box-shadow: 0 4px 20px rgba(139, 69, 19, 0.1);
}

.search-loader {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

.loader-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #8b4513;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.search-btn {
  background: linear-gradient(135deg, #c19f32 0%, #8b4513 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(139, 69, 19, 0.3);
}

/* Live Results */
.live-results {
  margin-top: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-height: 500px;
  overflow-y: auto;
}

.results-container {
  padding: 1.5rem;
}

.results-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.results-header h3 {
  color: #333;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.results-header p {
  color: #666;
  font-size: 0.9rem;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: #8b4513;
}

.result-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #c19f32 0%, #8b4513 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.result-info {
  flex: 1;
}

.result-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.result-name mark {
  background-color: #fff3cd;
  padding: 0.1rem 0.2rem;
  border-radius: 3px;
}

.result-family {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.result-family mark {
  background-color: #fff3cd;
  padding: 0.1rem 0.2rem;
  border-radius: 3px;
}

.result-details {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.result-details span {
  font-size: 0.8rem;
  color: #666;
}

.result-contact {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.result-contact span {
  font-size: 0.8rem;
  color: #666;
}

.result-actions {
  flex-shrink: 0;
}

.contact-btn {
  background-color: #8b4513;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.contact-btn:hover {
  background-color: #6d3410;
  transform: translateY(-1px);
}

/* No Results */
.no-results {
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.no-results-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-results h3 {
  color: #333;
  margin-bottom: 0.5rem;
}

.no-results p {
  margin-bottom: 0.5rem;
}

.suggestion {
  font-style: italic;
  color: #999;
}

/* Error Message */
.error-message {
  text-align: center;
  padding: 2rem;
  color: #dc3545;
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.retry-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 1rem;
}

.retry-btn:hover {
  background-color: #c82333;
}

/* Right Illustration */
.hero-illustration {
  position: relative;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.illustration-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
}

.circle-1 {
  width: 200px;
  height: 200px;
  background-color: #8b4513;
  top: 10%;
  right: 10%;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 150px;
  height: 150px;
  background-color: #c19f32;
  bottom: 20%;
  left: 10%;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 100px;
  height: 100px;
  background-color: #8b4513;
  top: 30%;
  left: 20%;
  animation: float 7s ease-in-out infinite;
}

.circle-4 {
  width: 80px;
  height: 80px;
  background-color: #c19f32;
  bottom: 40%;
  right: 30%;
  animation: float 5s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.people-illustration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.celebration-image {
  width: 350px;
  height: 250px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    gap: 1rem;
  }

  .nav.active .nav-links {
    display: flex;
  }

  .menu-toggle {
    display: block;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .search-input {
    font-size: 1rem;
  }

  .search-btn {
    width: 100%;
    font-size: 1rem;
  }

  .result-card {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .result-details,
  .result-contact {
    justify-content: center;
  }

  .celebration-image {
    width: 280px;
    height: 200px;
  }

  .hero-illustration {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 1rem;
  }

  .hero-search {
    padding: 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .search-container {
    margin-bottom: 1rem;
  }

  .live-results {
    margin-top: 1rem;
  }

  .celebration-image {
    width: 250px;
    height: 180px;
  }
}
