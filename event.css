/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Playfair Display", serif;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

/* Navigation Styles */
.navbar {
  background: rgba(106, 174, 216, 0.5);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo span {
  color: var(--black, #090a04);
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-size: 1.2rem;
}

.nav {
  display: flex;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 1.2rem;
  align-items: center;
}

.nav-links a {
  color: rgba(9, 10, 4, 0.6);
  font-family: "Playfair Display", serif;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 10px;
}

.nav-links .btn {
  background-color: transparent;
  border: 1px solid rgba(9, 10, 4, 0.6);
  border-radius: 5px;
  padding: 8px 16px;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: rgba(106, 174, 216, 0.5);
  margin-top: 10px;
  padding: 10px 0;
  border-radius: 5px;
  list-style: none;
  z-index: 999;
}

.dropdown-content li a {
  display: block;
  padding: 5px 15px;
  white-space: nowrap;
  color: rgba(9, 10, 4, 0.6);
}

.dropdown.show .dropdown-content {
  display: block;
}

/* Mobile menu toggle */
.menu-toggle {
  display: none;
  font-size: 24px;
  background: transparent;
  color: var(--black, #090a04);
  border: none;
  cursor: pointer;
}

/* Page Header */
.page-header {
  padding: 3rem 5%;
  background: linear-gradient(to bottom, #6aaed880 0%, #f8f8f8 17.5%);
  color: #333;
}

.header-content {
  text-align: center;
  margin-bottom: 2.5rem;
}

.header-content .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.page-header h1 {
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.page-header h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.page-header h1::after {
  content: "🎉";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  color: #c19f32;
}

.page-intro {
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Events Sections */
.events-section {
  padding: 4rem 5%;
  max-width: 1200px;
  margin: 0 auto;
}

.events-section:nth-child(even) {
  background: rgba(106, 174, 216, 0.05);
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  color: #090a04;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 500px;
  margin: 0 auto;
}

/* Events Grid */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

/* Event Cards */
.event-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-top: 4px solid #c19f32;
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.event-card.current {
  border-top-color: #dc3545;
}

.event-card.upcoming {
  border-top-color: #28a745;
}

.event-card.monthly {
  border-top-color: #6aaed8;
}

.event-card.seasonal {
  border-top-color: #c19f32;
}

/* Event Image */
.event-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.event-card:hover .event-image img {
  transform: scale(1.05);
}

/* Event Status/Countdown Badges */
.event-status,
.event-countdown,
.event-frequency,
.event-season {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.event-status {
  background: #dc3545;
  color: white;
  animation: pulse 2s infinite;
}

.event-countdown {
  background: #28a745;
  color: white;
}

.event-frequency {
  background: #6aaed8;
  color: white;
}

.event-season {
  background: #c19f32;
  color: white;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Event Content */
.event-content {
  padding: 1.5rem;
}

.event-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.event-date,
.event-location {
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.event-content h3 {
  font-size: 1.4rem;
  color: #090a04;
  margin-bottom: 0.8rem;
  font-weight: 700;
  line-height: 1.3;
}

.event-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Event Actions */
.event-actions {
  display: flex;
  gap: 0.8rem;
  flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Events Navigation */
.events-navigation {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 5%;
  text-align: center;
}

.nav-container h3 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 2rem;
  font-weight: 700;
}

.nav-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.nav-icon {
  font-size: 2rem;
}

.nav-link span:last-child {
  font-weight: 600;
  font-size: 1rem;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
    flex-wrap: wrap;
  }

  .logo span {
    font-size: 1.1rem;
  }

  .menu-toggle {
    display: block;
    order: 3;
  }

  .nav {
    display: none;
    width: 100%;
    order: 4;
    margin-top: 1rem;
  }

  .nav.active {
    display: block;
  }

  .nav-links {
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(106, 174, 216, 0.8);
    padding: 1rem;
    border-radius: 5px;
  }

  .nav-links a {
    padding: 10px;
    border-bottom: 1px solid rgba(9, 10, 4, 0.1);
  }

  .dropdown-content {
    position: static;
    display: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.1);
    margin-top: 5px;
  }

  .dropdown.show .dropdown-content {
    display: block;
  }

  .page-header {
    padding: 2rem 3%;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-intro {
    font-size: 1rem;
  }

  .events-section {
    padding: 2rem 3%;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .events-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .event-card {
    border-radius: 12px;
  }

  .event-image {
    height: 180px;
  }

  .event-content {
    padding: 1.2rem;
  }

  .event-content h3 {
    font-size: 1.2rem;
  }

  .event-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .event-actions {
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    text-align: center;
  }

  .events-navigation {
    padding: 2rem 3%;
  }

  .nav-container h3 {
    font-size: 1.5rem;
  }

  .nav-links-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .nav-link {
    padding: 1rem;
  }

  .nav-icon {
    font-size: 1.5rem;
  }

  .nav-link span:last-child {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.8rem;
  }

  .section-header h2 {
    font-size: 1.6rem;
  }

  .events-grid {
    gap: 1rem;
  }

  .event-image {
    height: 150px;
  }

  .event-content {
    padding: 1rem;
  }

  .event-content h3 {
    font-size: 1.1rem;
  }

  .event-status,
  .event-countdown,
  .event-frequency,
  .event-season {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }

  .nav-links-grid {
    grid-template-columns: 1fr;
  }

  .nav-link {
    padding: 0.8rem;
  }
}
