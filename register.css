/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Playfair Display", serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

/* Header Styles - White Background */
.navbar {
  background-color: #ffffff;
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar .logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #8b4513;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar .logo span {
  color: #333;
}

/* Navigation Styles */
.nav {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-links a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 0.5rem 0;
}

.nav-links a:hover {
  color: #8b4513;
}

.nav-links .btn {
  background-color: #8b4513;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.nav-links .btn:hover {
  background-color: #a0522d;
  color: white;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  min-width: 200px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  z-index: 1001;
  list-style: none;
  padding: 0.5rem 0;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content li {
  margin: 0;
}

.dropdown-content a {
  display: block;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.dropdown-content a:hover {
  background-color: #f8f9fa;
  color: #8b4513;
}

/* Mobile Menu */
.menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #333;
}

/* Registration Section */
.registration-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #f8f9fa;
  padding-top: calc(80px + 2rem);
}

.registration-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1200px;
  width: 100%;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

/* Image Section */
.image-section {
  position: relative;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Form Section */
.form-section {
  padding: 3rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  margin-bottom: 2rem;
  text-align: left;
}

.form-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.form-header p {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

/* Form Styles */
.registration-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.optional {
  color: #999;
  font-weight: 400;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  background-color: #f8f9fa;
}

.form-group input:focus {
  outline: none;
  border-color: #8b4513;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
  background-color: white;
}

/* Location Input */
.location-input {
  position: relative;
  display: flex;
  align-items: center;
}

.location-icon {
  position: absolute;
  left: 0.75rem;
  z-index: 1;
  font-size: 1rem;
}

.location-input input {
  padding-left: 2.5rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.btn-cancel {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  background-color: white;
  color: #666;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background-color: #f8f9fa;
  border-color: #999;
}

.btn-submit {
  flex: 2;
  padding: 0.75rem;
  border: none;
  background-color: #d4a574;
  color: white;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-submit:hover {
  background-color: #c19a5b;
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-toggle {
    display: block;
  }

  .nav-links {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    gap: 1rem;
    z-index: 1002;
  }

  .nav.active .nav-links {
    display: flex;
  }

  .registration-section {
    padding: 1rem;
    padding-top: calc(80px + 1rem);
  }

  .registration-container {
    grid-template-columns: 1fr;
    margin: 0;
  }

  .image-section {
    height: 250px;
  }

  .form-section {
    padding: 2rem 1.5rem;
  }

  .form-header h1 {
    font-size: 1.75rem;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 1rem;
  }

  .registration-section {
    padding: 1rem;
  }

  .form-section {
    padding: 1.5rem 1rem;
  }

  .form-header h1 {
    font-size: 1.5rem;
  }
}

/* Message Container Styles */
.message-container {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  position: relative;
  border-left: 4px solid;
  animation: slideDown 0.3s ease-out;
}

.message-container.success {
  background-color: #d4edda;
  border-left-color: #28a745;
  color: #155724;
}

.message-container.error {
  background-color: #f8d7da;
  border-left-color: #dc3545;
  color: #721c24;
}

.message-container.info {
  background-color: #d1ecf1;
  border-left-color: #17a2b8;
  color: #0c5460;
}

.message-content {
  margin-right: 2rem;
}

.message-content h3,
.message-content h4 {
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.message-content p {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.message-content ul {
  margin-left: 1rem;
  margin-bottom: 0.5rem;
}

.message-content li {
  margin-bottom: 0.25rem;
}

.close-message {
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.close-message:hover {
  opacity: 1;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading Spinner Styles */
.btn-loader {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.spinner {
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

.spinner .path {
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
