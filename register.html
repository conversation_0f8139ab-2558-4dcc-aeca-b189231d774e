<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Register - <PERSON><PERSON>-<PERSON><PERSON>ti</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="register.css" />
    <script src="javascript.js" defer></script>
  </head>
  <body>
    <header class="navbar">
      <div class="logo">👑 <span>OSI-EKITI</span></div>

      <!-- Mobile Menu Button -->
      <button class="menu-toggle" id="menuToggle">&#9776;</button>

      <nav class="nav" id="navMenu">
        <ul class="nav-links">
          <li><a href="index.html">Home</a></li>
          <li class="dropdown">
            <a href="#" class="dropdown-toggle">About Us ▾</a>
            <ul class="dropdown-content">
              <li><a href="about.html#our-history">Our History</a></li>
              <li>
                <a href="about.html#rulling-house">The Ruling Houses</a>
              </li>
              <li>
                <a href="about.html#functional-groups"
                  >The Functional Groups & Authority</a
                >
              </li>
              <li>
                <a href="about.html#council-of-chiefs">The Council Of Chiefs</a>
              </li>
              <li><a href="about.html#quarters">The Quarters</a></li>
              <li>
                <a href="about.html#traditional-age-groups"
                  >The Traditional Age Groups</a
                >
              </li>
              <li>
                <a href="about.html#club-and-societies">Club & Societies</a>
              </li>
              <li>
                <a href="about.html#place-of-intrest">Places of Interest</a>
              </li>
            </ul>
          </li>
          <li class="dropdown">
            <a href="#" class="dropdown-toggle">Community ▾</a>
            <ul class="dropdown-content">
              <li><a href="register.html">Register As Omo Osi</a></li>
              <li><a href="search.html">Search for Family & Friends</a></li>
            </ul>
          </li>
          <li class="dropdown">
            <a href="#" class="dropdown-toggle">Events ▾</a>
            <ul class="dropdown-content">
              <li><a href="events.html#current-events">Current Events</a></li>
              <li><a href="events.html#upcoming-events">Upcoming Events</a></li>
              <li><a href="events.html#monthly-events">Monthly Events</a></li>
              <li><a href="events.html#seasonal-events">Seasonal Events</a></li>
            </ul>
          </li>
          <li><a href="information-center.html">Information Center</a></li>
          <li><a href="projects.html">Projects</a></li>
          <li><a href="contact-us.html">Contact Us</a></li>
          <li><a href="donate.html" class="btn">Donate</a></li>
        </ul>
      </nav>
    </header>

    <!-- Registration Section -->
    <section class="registration-section">
      <div class="registration-container">
        <!-- Left Side - Image -->
        <div class="image-section">
          <img
            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=center"
            alt="Omo Osi"
            class="hero-image"
          />
        </div>

        <!-- Right Side - Form -->
        <div class="form-section">
          <div class="form-container">
            <div class="form-header">
              <h1>Claim Your Roots as an Omo Osi</h1>
              <p>Join our growing digital family and stay connected to home.</p>
            </div>

            <!-- Message Container -->
            <div
              id="messageContainer"
              class="message-container"
              style="display: none"
            >
              <div id="messageContent" class="message-content"></div>
              <button id="closeMessage" class="close-message">&times;</button>
            </div>

            <form class="registration-form" id="registrationForm" novalidate>
              <div class="form-group">
                <label for="fullName">Full Name</label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  placeholder="Your name"
                />
              </div>

              <div class="form-group">
                <label for="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="<EMAIL>"
                />
              </div>

              <div class="form-group">
                <label for="familyName">Family Name</label>
                <input
                  type="text"
                  id="familyName"
                  name="familyName"
                  placeholder="Your family name"
                />
              </div>

              <div class="form-group">
                <label for="phone">Phone number</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  placeholder="+234-************"
                />
              </div>

              <div class="form-group">
                <label for="quarters">Quarters</label>
                <select id="quarters" name="quarters" required>
                  <option value="">Select your quarters</option>
                  <option value="Isale Osi">Isale Osi</option>
                  <option value="Oke Osi">Oke Osi</option>
                  <option value="Agbado">Agbado</option>
                  <option value="Odo Osi">Odo Osi</option>
                  <option value="Oke Agbe">Oke Agbe</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div class="form-group">
                <label for="location">Current location</label>
                <div class="location-input">
                  <span class="location-icon">📍</span>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    placeholder="Lagos, NG"
                    required
                  />
                </div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn-cancel">Cancel</button>
                <button type="submit" class="btn-submit" id="submitBtn">
                  <span id="btnText">Register Now</span>
                  <span id="btnLoader" class="btn-loader" style="display: none">
                    <svg class="spinner" viewBox="0 0 50 50">
                      <circle
                        class="path"
                        cx="25"
                        cy="25"
                        r="20"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="5"
                        stroke-linecap="round"
                      ></circle>
                    </svg>
                    Registering...
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const form = document.getElementById("registrationForm");
        const submitBtn = document.getElementById("submitBtn");
        const cancelBtn = document.querySelector(".btn-cancel");
        const messageContainer = document.getElementById("messageContainer");
        const messageContent = document.getElementById("messageContent");
        const closeMessage = document.getElementById("closeMessage");

        // Function to show messages
        function showMessage(message, type = "info") {
          messageContent.innerHTML = message;
          messageContainer.className = `message-container ${type}`;
          messageContainer.style.display = "block";

          // Auto-hide success messages after 5 seconds
          if (type === "success") {
            setTimeout(() => {
              hideMessage();
            }, 5000);
          }
        }

        // Function to hide messages
        function hideMessage() {
          messageContainer.style.display = "none";
        }

        // Close message button
        closeMessage.addEventListener("click", hideMessage);

        // Handle form submission
        form.addEventListener("submit", async function (e) {
          e.preventDefault();

          // Hide any existing messages
          hideMessage();

          // Show loading state with spinner
          const btnText = document.getElementById("btnText");
          const btnLoader = document.getElementById("btnLoader");

          btnText.style.display = "none";
          btnLoader.style.display = "inline-flex";
          submitBtn.disabled = true;

          try {
            // Get form data
            const formData = new FormData(form);
            const userData = {
              fullname: formData.get("fullName"),
              email: formData.get("email"),
              family_name: formData.get("familyName"),
              phone_no: formData.get("phone"),
              quarters: formData.get("quarters"),
              current_address: formData.get("location"),
            };

            // Validate required fields on frontend
            const requiredFields = [
              "fullname",
              "email",
              "family_name",
              "phone_no",
              "quarters",
              "current_address",
            ];
            const missingFields = [];

            for (const field of requiredFields) {
              if (!userData[field] || userData[field].trim() === "") {
                missingFields.push(field.replace("_", " "));
              }
            }

            if (missingFields.length > 0) {
              showMessage(
                `Please fill in the following required fields: ${missingFields.join(
                  ", "
                )}`,
                "error"
              );
              return;
            }

            // Send to API - Correct endpoint
            console.log("Sending request to API...", userData);

            const response = await fetch("/osi_ekiti_node/api/users", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(userData),
            });

            console.log("Response status:", response.status);
            console.log("Response headers:", response.headers);

            const result = await response.json();
            console.log("Response data:", result);

            if (response.ok && result.success) {
              // Success
              showMessage(
                `
                <h3>🎉 Registration Successful!</h3>
                <p>Welcome to the Osi-Ekiti community, <strong>${userData.fullname}</strong>!</p>
                <p>You have been successfully registered from <strong>${userData.quarters}</strong> quarters.</p>
                <p>You can now search for family and friends in our community directory.</p>
              `,
                "success"
              );
              form.reset();
            } else {
              // Error from server
              let errorMessage = "Registration failed. Please try again.";

              if (result.error) {
                errorMessage = result.error;
              }

              // Handle validation errors
              if (result.details && Array.isArray(result.details)) {
                errorMessage = `
                  <h4>Please fix the following errors:</h4>
                  <ul>
                    ${result.details
                      .map((error) => `<li>${error}</li>`)
                      .join("")}
                  </ul>
                `;
              }

              showMessage(errorMessage, "error");
            }
          } catch (error) {
            console.error("Registration error:", error);

            // Check if it's a network error or parsing error
            if (error.name === "SyntaxError") {
              showMessage(
                `
                <h4>Server Response Error</h4>
                <p>The server returned an invalid response. This might be a server configuration issue.</p>
                <p><small>Please check the browser console for more details.</small></p>
              `,
                "error"
              );
            } else if (
              error.name === "TypeError" &&
              error.message.includes("fetch")
            ) {
              showMessage(
                `
                <h4>Connection Error</h4>
                <p>Unable to connect to the server. Please check:</p>
                <ul>
                  <li>Your internet connection</li>
                  <li>That XAMPP is running</li>
                  <li>That the API endpoint is accessible</li>
                </ul>
              `,
                "error"
              );
            } else {
              showMessage(
                `
                <h4>Unexpected Error</h4>
                <p>An unexpected error occurred: ${error.message}</p>
                <p><small>Please check the browser console for more details.</small></p>
              `,
                "error"
              );
            }
          } finally {
            // Reset button state
            btnText.style.display = "inline";
            btnLoader.style.display = "none";
            submitBtn.disabled = false;
          }
        });

        // Handle cancel button
        cancelBtn.addEventListener("click", function () {
          if (confirm("Are you sure you want to cancel registration?")) {
            form.reset();
            hideMessage();
          }
        });
      });
    </script>
  </body>
</html>
