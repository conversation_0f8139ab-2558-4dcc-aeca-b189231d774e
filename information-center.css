/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Playfair Display", serif;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

/* Navigation Styles (matching index.html) */
.navbar {
  background: rgba(106, 174, 216, 0.5);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo span {
  color: var(--black, #090a04);
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-size: 1.2rem;
}

.nav {
  display: flex;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 1.2rem;
  align-items: center;
}

.nav-links a {
  color: rgba(9, 10, 4, 0.6);
  font-family: "Playfair Display", serif;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 10px;
}

.nav-links .btn {
  background-color: transparent;
  border: 1px solid rgba(9, 10, 4, 0.6);
  border-radius: 5px;
  padding: 8px 16px;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: rgba(106, 174, 216, 0.5);
  margin-top: 10px;
  padding: 10px 0;
  border-radius: 5px;
  list-style: none;
  z-index: 999;
}

.dropdown-content li a {
  display: block;
  padding: 5px 15px;
  white-space: nowrap;
  color: rgba(9, 10, 4, 0.6);
}

.dropdown.show .dropdown-content {
  display: block;
}

/* Mobile menu toggle */
.menu-toggle {
  display: none;
  font-size: 24px;
  background: transparent;
  color: var(--black, #090a04);
  border: none;
  cursor: pointer;
}

/* Page Header */
.page-header {
  padding: 3rem 5%;
  background: linear-gradient(to bottom, #6aaed880 0%, #f8f8f8 17.5%);
  color: #333;
}

.header-content {
  text-align: center;
  margin-bottom: 2.5rem;
}

.header-content .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.page-header h1 {
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.page-header h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.page-header h1::after {
  content: "📢";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  color: #c19f32;
}

.page-intro {
  font-size: 1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Notification Sections */
.notification-section {
  padding: 3rem 5%;
  max-width: 1000px;
  margin: 0 auto;
}

.notification-section:nth-child(even) {
  background: rgba(106, 174, 216, 0.05);
}

.section-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.section-header h2 {
  font-size: 2rem;
  color: #090a04;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.section-header p {
  font-size: 1rem;
  color: #666;
  max-width: 500px;
  margin: 0 auto;
}

/* Notification Cards */
.notification-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: 5px solid #c19f32;
}

.notification-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.notification-card.palace {
  border-left-color: #c19f32;
}

.notification-card.opu {
  border-left-color: #6aaed8;
}

.notification-card.general {
  border-left-color: #28a745;
}

.notification-card.congratulatory {
  border-left-color: #ff6b6b;
}

/* Notification Header */
.notification-header {
  padding: 1.5rem 1.5rem 0;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.notification-date {
  font-size: 0.9rem;
  color: #666;
}

.notification-priority {
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.palace .notification-priority {
  background: rgba(193, 159, 50, 0.1);
  color: #c19f32;
}

.opu .notification-priority {
  background: rgba(106, 174, 216, 0.1);
  color: #6aaed8;
}

.general .notification-priority {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.congratulatory .notification-priority {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
}

.notification-header h3 {
  font-size: 1.4rem;
  color: #090a04;
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: 0;
}

/* Notification Content */
.notification-content {
  padding: 0 1.5rem 1.5rem;
}

.notification-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.notification-content p:last-of-type {
  margin-bottom: 1.5rem;
}

.notification-details {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.notification-details span {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #555;
}

.notification-details span:last-child {
  margin-bottom: 0;
}

/* Notification Actions */
.notification-actions {
  padding: 0 1.5rem 1.5rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
  padding: 0.7rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: #c19f32;
  color: white;
}

.btn-primary:hover {
  background: #a68929;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(193, 159, 50, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #6aaed8;
  border: 2px solid #6aaed8;
}

.btn-secondary:hover {
  background: #6aaed8;
  color: white;
  transform: translateY(-2px);
}

/* Quick Navigation */
.quick-navigation {
  background: linear-gradient(135deg, #6aaed8 0%, #c19f32 100%);
  padding: 3rem 5%;
  text-align: center;
}

.nav-container h3 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 2rem;
  font-weight: 700;
}

.nav-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.nav-icon {
  font-size: 2rem;
}

.nav-link span:last-child {
  font-weight: 600;
  font-size: 1rem;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
    flex-wrap: wrap;
  }

  .logo span {
    font-size: 1.1rem;
  }

  .menu-toggle {
    display: block;
    order: 3;
  }

  .nav {
    display: none;
    width: 100%;
    order: 4;
    margin-top: 1rem;
  }

  .nav.active {
    display: block;
  }

  .nav-links {
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(106, 174, 216, 0.8);
    padding: 1rem;
    border-radius: 5px;
  }

  .nav-links a {
    padding: 10px;
    border-bottom: 1px solid rgba(9, 10, 4, 0.1);
  }

  .dropdown-content {
    position: static;
    display: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.1);
    margin-top: 5px;
  }

  .dropdown.show .dropdown-content {
    display: block;
  }

  .page-header {
    padding: 2rem 3%;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .notification-section {
    padding: 2rem 3%;
  }

  .section-header h2 {
    font-size: 1.6rem;
  }

  .notification-header {
    padding: 1rem 1rem 0;
  }

  .notification-content {
    padding: 0 1rem 1rem;
  }

  .notification-actions {
    padding: 0 1rem 1rem;
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    text-align: center;
  }

  .notification-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .quick-navigation {
    padding: 2rem 3%;
  }

  .nav-container h3 {
    font-size: 1.5rem;
  }

  .nav-links-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .nav-link {
    padding: 1rem;
  }

  .nav-icon {
    font-size: 1.5rem;
  }

  .nav-link span:last-child {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 1.8rem;
  }

  .section-header h2 {
    font-size: 1.4rem;
  }

  .notification-header h3 {
    font-size: 1.2rem;
  }

  .notification-content p {
    font-size: 0.9rem;
  }

  .notification-details {
    padding: 0.8rem;
  }

  .notification-details span {
    font-size: 0.8rem;
  }

  .nav-links-grid {
    grid-template-columns: 1fr;
  }

  .nav-link {
    padding: 0.8rem;
  }
}
