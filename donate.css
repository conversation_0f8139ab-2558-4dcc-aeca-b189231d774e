/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Playfair Display", serif;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

/* Navigation Styles (Matching your project) */
.navbar {
  background: rgba(106, 174, 216, 0.5);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo span {
  color: var(--black, #090a04);
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: bold;
  font-size: 1.2rem;
}

.nav {
  display: flex;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 1.2rem;
  align-items: center;
}

.nav-links a {
  color: rgba(9, 10, 4, 0.6);
  font-family: "Playfair Display", serif;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 10px;
}

.nav-links .btn {
  background-color: transparent;
  border: 1px solid rgba(9, 10, 4, 0.6);
  border-radius: 5px;
  padding: 8px 16px;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: rgba(106, 174, 216, 0.5);
  margin-top: 10px;
  padding: 10px 0;
  border-radius: 5px;
  list-style: none;
  z-index: 999;
}

.dropdown-content li a {
  display: block;
  padding: 5px 15px;
  white-space: nowrap;
  color: rgba(9, 10, 4, 0.6);
}

.dropdown.show .dropdown-content {
  display: block;
}

/* Mobile menu toggle */
.menu-toggle {
  display: none;
  font-size: 24px;
  background: transparent;
  color: var(--black, #090a04);
  border: none;
  cursor: pointer;
}

/* Donation Section */
.donation-section {
  padding: 3rem 5%;
  background: linear-gradient(to bottom, #6aaed880 0%, #f8f8f8 17.5%);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.donation-container {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.donation-header {
  text-align: center;
  margin-bottom: 3rem;
}

.donation-header .label {
  display: inline-block;
  font-size: 12px;
  color: #c19f32;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.donation-header h1 {
  color: var(--black, #090a04);
  font-size: 2.5rem;
  font-weight: bold;
  font-family: "Playfair Display", serif;
  margin-bottom: 1rem;
  text-transform: capitalize;
  position: relative;
}

.donation-header h1::before {
  content: "";
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #c19f32;
  border-radius: 2px;
}

.donation-header h1::after {
  content: "💝";
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.8rem;
  color: #c19f32;
}

.donation-intro {
  font-size: 1.1rem;
  color: var(--black, #090a04);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Donation Card */
.donation-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-top: 4px solid #c19f32;
}

.card-header {
  background: linear-gradient(135deg, #6aaed8 0%, #c19f32 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.card-header .icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.card-header h2 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.card-header p {
  opacity: 0.9;
  font-size: 1rem;
}

/* Account Details */
.account-details {
  padding: 2rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem;
  margin-bottom: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #6aaed8;
  position: relative;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #090a04;
  font-size: 0.9rem;
}

.detail-icon {
  font-size: 1.2rem;
}

.detail-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #c19f32;
  font-family: "Playfair Display", serif;
}

.copy-btn {
  background: #6aaed8;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 1rem;
}

.copy-btn:hover {
  background: #5a9bc8;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(106, 174, 216, 0.3);
}

/* Donation Note */
.donation-note {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 10px;
  padding: 1.5rem;
  margin: 2rem;
  display: flex;
  gap: 1rem;
}

.note-icon {
  font-size: 1.5rem;
  color: #c19f32;
}

.note-content h3 {
  color: #090a04;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.note-content p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Donation Actions */
.donation-actions {
  padding: 2rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn-primary {
  background: #c19f32;
  color: white;
  border: 2px solid #c19f32;
}

.btn-primary:hover {
  background: #a68929;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(193, 159, 50, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #6aaed8;
  border: 2px solid #6aaed8;
}

.btn-secondary:hover {
  background: #6aaed8;
  color: white;
  transform: translateY(-2px);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
    flex-wrap: wrap;
  }

  .logo span {
    font-size: 1.1rem;
  }

  .menu-toggle {
    display: block;
    order: 3;
  }

  .nav {
    display: none;
    width: 100%;
    order: 4;
    margin-top: 1rem;
  }

  .nav.active {
    display: block;
  }

  .nav-links {
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(106, 174, 216, 0.8);
    padding: 1rem;
    border-radius: 5px;
  }

  .nav-links a {
    padding: 10px;
    border-bottom: 1px solid rgba(9, 10, 4, 0.1);
  }

  .dropdown-content {
    position: static;
    display: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.1);
    margin-top: 5px;
  }

  .dropdown.show .dropdown-content {
    display: block;
  }

  /* Donation Section Mobile */
  .donation-section {
    padding: 2rem 3%;
    min-height: auto;
  }

  .donation-header h1 {
    font-size: 2rem;
  }

  .donation-intro {
    font-size: 1rem;
  }

  .card-header {
    padding: 1.5rem;
  }

  .card-header .icon {
    font-size: 2.5rem;
  }

  .card-header h2 {
    font-size: 1.5rem;
  }

  .account-details {
    padding: 1.5rem;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;
    padding: 1rem;
  }

  .detail-value {
    font-size: 1rem;
  }

  .copy-btn {
    margin-left: 0;
    align-self: flex-end;
  }

  .donation-note {
    margin: 1.5rem;
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }

  .donation-actions {
    padding: 1.5rem;
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .donation-header h1 {
    font-size: 1.8rem;
  }

  .donation-intro {
    font-size: 0.95rem;
  }

  .card-header {
    padding: 1rem;
  }

  .card-header .icon {
    font-size: 2rem;
  }

  .card-header h2 {
    font-size: 1.3rem;
  }

  .account-details {
    padding: 1rem;
  }

  .detail-item {
    padding: 0.8rem;
  }

  .detail-label {
    font-size: 0.85rem;
  }

  .detail-value {
    font-size: 0.95rem;
  }

  .donation-note {
    margin: 1rem;
    padding: 0.8rem;
  }

  .note-content h3 {
    font-size: 1rem;
  }

  .note-content p {
    font-size: 0.9rem;
  }

  .donation-actions {
    padding: 1rem;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.7rem 1.2rem;
    font-size: 0.85rem;
  }
}
